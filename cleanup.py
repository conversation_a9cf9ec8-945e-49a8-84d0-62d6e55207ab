#!/usr/bin/env python3
import csv
import json
import boto3
from botocore.exceptions import ClientError

CLOUDTRAIL_CSV = "cloudtrail.csv"   # Change to your CSV file path
DRY_RUN = True                      # Set False for cleanup (terminations)
DISABLE_REGIONS = False             # Set True to disable opt-in regions

# Initialize boto3 clients
session = boto3.Session()
ec2_clients = {}
asg_client = session.client('autoscaling')
iam_client = session.client('iam')
account_client = session.client('account')

resources_to_process = {
    "instances": set(),
    "volumes": set(),
    "network_interfaces": set(),
    "security_groups": set(),
    "auto_scaling_groups": set(),
    "vpcs": set(),
    "subnets": set(),
    "other": set()
}

print(f"[INFO] Reading CloudTrail log from {CLOUDTRAIL_CSV}")

with open(CLOUDTRAIL_CSV, newline='', encoding="utf-8") as csvfile:
    reader = csv.DictReader(csvfile, delimiter='\t')
    for row in reader:
        resources_field = row.get('Resources', "")
        region = row.get('AWS region', "us-east-1")

        # Skip empty Resources
        if not resources_field.strip():
            continue

        try:
            resources = json.loads(resources_field)
        except json.JSONDecodeError:
            continue

        for res in resources:
            resource_name = res.get("resourceName")
            resource_type = res.get("resourceType")

            if not resource_name:
                continue

            if resource_name.startswith("i-"):
                resources_to_process["instances"].add((resource_name, region))
            elif resource_name.startswith("vol-"):
                resources_to_process["volumes"].add((resource_name, region))
            elif resource_name.startswith("eni-"):
                resources_to_process["network_interfaces"].add((resource_name, region))
            elif resource_name.startswith("sg-"):
                resources_to_process["security_groups"].add((resource_name, region))
            elif resource_name.startswith("vpc-"):
                resources_to_process["vpcs"].add((resource_name, region))
            elif resource_name.startswith("subnet-"):
                resources_to_process["subnets"].add((resource_name, region))
            elif "AutoScalingGroup" in (resource_type or ""):
                resources_to_process["auto_scaling_groups"].add((resource_name, region))
            else:
                resources_to_process["other"].add((resource_name, region))

# ===== DRY RUN OUTPUT =====
print("\n[DRY RUN] Resources discovered:")
for category, items in resources_to_process.items():
    print(f"\n-- {category.upper()} --")
    for name, region in items:
        print(f"  {name} ({region})")

# ===== Disable opt-in regions if requested =====
if DISABLE_REGIONS:
    print("\n[INFO] Disabling opt-in AWS regions...")
    try:
        regions = account_client.list_regions(
            RegionOptStatusContains=['ENABLED', 'ENABLED_BY_DEFAULT']
        )
        for r in regions['Regions']:
            if r['OptStatus'] == 'ENABLED':
                print(f"[ACTION] Disabling region: {r['RegionName']}")
                if not DRY_RUN:
                    account_client.disable_region(RegionName=r['RegionName'])
    except ClientError as e:
        print(f"[ERROR] {e}")

if DRY_RUN:
    print("\n[INFO] Dry run complete. No deletions performed.")
    exit(0)

# ===== Cleanup Logic (executed if DRY_RUN=False) =====
print("\n[INFO] Cleaning up resources...")
for instance_id, region in resources_to_process["instances"]:
    ec2 = session.client('ec2', region_name=region)
    try:
        ec2.terminate_instances(InstanceIds=[instance_id])
        print(f"[DELETE] EC2 instance: {instance_id}")
    except ClientError as e:
        print(f"[ERROR] Terminating {instance_id}: {e}")

# Similar delete blocks can be added for volumes, ENIs, ASGs, etc.
